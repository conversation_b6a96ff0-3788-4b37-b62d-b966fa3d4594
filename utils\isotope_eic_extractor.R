# 同位素内标离子EIC数据提取器
# 从数据库中提取同位素内标离子的EIC数据和对应的MS2碎片信息

# 加载必要的包和脚本
source("utils/database_schema.R")
source("utils/path_manager.R")
source("utils/logger.R")

# 加载必要的包
load_eic_extractor_packages <- function() {
  required_packages <- c("DBI", "RSQLite", "dplyr", "jsonlite")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(DBI)
  library(RSQLite)
  library(dplyr)
  library(jsonlite)
}

# 同位素内标EIC提取器类
IsotopeEICExtractor <- setRefClass("IsotopeEICExtractor",
  fields = list(
    db_connection = "ANY",
    tolerance_ppm = "numeric",
    min_intensity = "numeric"
  ),
  methods = list(
    # 初始化
    initialize = function(db_path = NULL, tolerance_ppm = 20, min_intensity = 1000) {
      tolerance_ppm <<- tolerance_ppm
      min_intensity <<- min_intensity
      
      if (!is.null(db_path)) {
        connect_database(db_path)
      }
    },
    
    # 连接数据库
    connect_database = function(db_path) {
      if (!file.exists(db_path)) {
        stop("数据库文件不存在:", db_path)
      }
      
      if (!is.null(db_connection)) {
        DBI::dbDisconnect(db_connection)
      }
      
      db_connection <<- DBI::dbConnect(RSQLite::SQLite(), db_path)
      
      # 启用外键约束
      DBI::dbExecute(db_connection, "PRAGMA foreign_keys = ON;")
      
      log_info(paste("已连接到数据库:", db_path))
    },
    
    # 断开数据库连接
    disconnect_database = function() {
      if (!is.null(db_connection)) {
        DBI::dbDisconnect(db_connection)
        db_connection <<- NULL
        log_info("已断开数据库连接")
      }
    },
    
    # 根据扫描模式和离子类型匹配文件
    match_files_by_scan_mode = function(scan_mode, ion_type) {
      if (is.null(db_connection)) {
        stop("数据库未连接")
      }
      
      # 根据离子类型确定极性
      polarity <- if (ion_type == "[M+H]+") 1 else if (ion_type == "[M-H]-") 0 else NULL
      
      if (is.null(polarity)) {
        stop("不支持的离子类型:", ion_type)
      }
      
      # 构建查询SQL
      sql <- "
        SELECT DISTINCT df.file_id, df.file_name, df.sample_type, df.scan_mode
        FROM data_files df
        INNER JOIN ms1_spectra_data ms1 ON df.file_id = ms1.file_id
        WHERE df.scan_mode = ? AND ms1.polarity = ?
      "
      
      result <- DBI::dbGetQuery(db_connection, sql, params = list(scan_mode, polarity))
      
      log_info(paste("匹配到", nrow(result), "个文件，扫描模式:", scan_mode, "离子类型:", ion_type))
      
      return(result)
    },
    
    # 根据质荷比匹配MS1峰数据
    match_ms1_peaks_by_mz = function(target_mz, file_ids = NULL, tolerance_ppm = 20) {
      if (is.null(db_connection)) {
        stop("数据库未连接")
      }
      
      # 计算质量偏差范围
      mz_tolerance <- target_mz * tolerance_ppm / 1e6
      mz_min <- target_mz - mz_tolerance
      mz_max <- target_mz + mz_tolerance
      
      # 构建查询SQL
      base_sql <- "
        SELECT 
          p.peak_id,
          p.spectrum_id,
          p.mz,
          p.intensity,
          s.file_id,
          s.rtime,
          s.scan_index,
          ABS(p.mz - ?) as mz_error,
          (ABS(p.mz - ?) / ? * 1e6) as mz_error_ppm
        FROM ms1_peaks_data p
        INNER JOIN ms1_spectra_data s ON p.spectrum_id = s.spectrum_id
        WHERE p.mz BETWEEN ? AND ?
          AND p.intensity >= ?
      "
      
      params <- list(target_mz, target_mz, target_mz, mz_min, mz_max, min_intensity)
      
      # 如果指定了文件ID，添加文件过滤条件
      if (!is.null(file_ids) && length(file_ids) > 0) {
        file_placeholders <- paste(rep("?", length(file_ids)), collapse = ",")
        base_sql <- paste(base_sql, "AND s.file_id IN (", file_placeholders, ")")
        params <- c(params, as.list(file_ids))
      }
      
      # 按质量偏差和强度排序
      base_sql <- paste(base_sql, "ORDER BY mz_error_ppm ASC, p.intensity DESC")
      
      result <- DBI::dbGetQuery(db_connection, base_sql, params = params)
      
      log_info(paste("匹配到", nrow(result), "个MS1峰，目标m/z:", target_mz, "容差:", tolerance_ppm, "ppm"))

      return(result)
    },

    # 根据MS1峰ID查找对应的MS2信息
    find_ms2_by_ms1_peak_ids = function(peak_ids) {
      if (is.null(db_connection)) {
        stop("数据库未连接")
      }

      if (length(peak_ids) == 0) {
        return(data.frame())
      }

      # 构建查询SQL
      placeholders <- paste(rep("?", length(peak_ids)), collapse = ",")
      sql <- paste("
        SELECT
          ms2.spectrum_id,
          ms2.file_id,
          ms2.scan_index,
          ms2.rtime,
          ms2.prec_scan_num,
          ms2.precursor_mz,
          ms2.precursor_intensity,
          ms2.precursor_charge,
          ms2.collision_energy,
          ms2.isolation_window_lower_mz,
          ms2.isolation_window_target_mz,
          ms2.isolation_window_upper_mz,
          ms2.ms1_peak_id,
          ms2.peaks_count,
          ms2.tot_ion_current,
          ms2.base_peak_mz,
          ms2.base_peak_intensity
        FROM ms2_spectra_data ms2
        WHERE ms2.ms1_peak_id IN (", placeholders, ")
        ORDER BY ms2.file_id, ms2.rtime
      ")

      result <- DBI::dbGetQuery(db_connection, sql, params = as.list(peak_ids))

      log_info(paste("找到", nrow(result), "个MS2谱图，对应", length(peak_ids), "个MS1峰"))

      return(result)
    },

    # 根据MS2谱图ID查找碎片离子数据
    find_ms2_fragments = function(ms2_spectrum_ids) {
      if (is.null(db_connection)) {
        stop("数据库未连接")
      }

      if (length(ms2_spectrum_ids) == 0) {
        return(data.frame())
      }

      # 构建查询SQL
      placeholders <- paste(rep("?", length(ms2_spectrum_ids)), collapse = ",")
      sql <- paste("
        SELECT
          p.peak_id,
          p.spectrum_id,
          p.mz,
          p.intensity
        FROM ms2_peaks_data p
        WHERE p.spectrum_id IN (", placeholders, ")
        ORDER BY p.spectrum_id, p.intensity DESC
      ")

      result <- DBI::dbGetQuery(db_connection, sql, params = as.list(ms2_spectrum_ids))

      log_info(paste("找到", nrow(result), "个MS2碎片离子，对应", length(ms2_spectrum_ids), "个MS2谱图"))

      return(result)
    },

    # 提取单个同位素内标的EIC数据
    extract_isotope_eic = function(isotope_ion) {
      # 验证输入参数
      required_fields <- c("离子质荷比", "离子类型", "离子化模式", "化合物名称")
      missing_fields <- setdiff(required_fields, names(isotope_ion))
      if (length(missing_fields) > 0) {
        stop("缺少必要字段:", paste(missing_fields, collapse = ", "))
      }

      target_mz <- isotope_ion$离子质荷比
      ion_type <- isotope_ion$离子类型
      ionization_mode <- isotope_ion$离子化模式
      compound_name <- isotope_ion$化合物名称

      log_info(paste("开始提取EIC数据:", compound_name, "m/z:", target_mz, "离子类型:", ion_type))

      # 根据离子化模式确定扫描模式
      scan_mode <- if (ionization_mode == "positive") "positive" else "negative"

      # 1. 匹配文件
      matched_files <- match_files_by_scan_mode(scan_mode, ion_type)

      if (nrow(matched_files) == 0) {
        log_warning(paste("未找到匹配的文件:", compound_name))
        return(NULL)
      }

      # 2. 匹配MS1峰数据
      ms1_peaks <- match_ms1_peaks_by_mz(target_mz, matched_files$file_id)

      if (nrow(ms1_peaks) == 0) {
        log_warning(paste("未找到匹配的MS1峰:", compound_name))
        return(NULL)
      }

      # 3. 查找对应的MS2信息
      ms2_spectra <- find_ms2_by_ms1_peak_ids(ms1_peaks$peak_id)

      # 4. 查找MS2碎片信息
      ms2_fragments <- data.frame()
      if (nrow(ms2_spectra) > 0) {
        ms2_fragments <- find_ms2_fragments(ms2_spectra$spectrum_id)
      }

      # 5. 组织EIC数据结构
      eic_data <- list(
        isotope_info = list(
          compound_name = compound_name,
          target_mz = target_mz,
          ion_type = ion_type,
          ionization_mode = ionization_mode,
          retention_time = isotope_ion$保留时间 %||% NA,
          molecular_weight = isotope_ion$分子质量 %||% NA
        ),
        matched_files = matched_files,
        ms1_peaks = ms1_peaks,
        ms2_spectra = ms2_spectra,
        ms2_fragments = ms2_fragments,
        extraction_time = Sys.time(),
        extraction_parameters = list(
          tolerance_ppm = tolerance_ppm,
          min_intensity = min_intensity
        )
      )

      log_info(paste("EIC数据提取完成:", compound_name,
                    "MS1峰:", nrow(ms1_peaks),
                    "MS2谱图:", nrow(ms2_spectra),
                    "MS2碎片:", nrow(ms2_fragments)))

      return(eic_data)
    },

    # 批量提取多个同位素内标的EIC数据
    extract_multiple_isotope_eic = function(isotope_ions_list) {
      if (length(isotope_ions_list) == 0) {
        log_warning("同位素内标列表为空")
        return(list())
      }

      log_info(paste("开始批量提取", length(isotope_ions_list), "个同位素内标的EIC数据"))

      results <- list()
      successful_count <- 0
      failed_count <- 0

      for (i in seq_along(isotope_ions_list)) {
        isotope_ion <- isotope_ions_list[[i]]
        compound_name <- isotope_ion$化合物名称 %||% paste("化合物", i)

        tryCatch({
          eic_data <- extract_isotope_eic(isotope_ion)
          if (!is.null(eic_data)) {
            results[[compound_name]] <- eic_data
            successful_count <- successful_count + 1
          } else {
            failed_count <- failed_count + 1
          }
        }, error = function(e) {
          log_error(paste("提取EIC数据失败:", compound_name, "-", e$message))
          failed_count <<- failed_count + 1
        })
      }

      log_info(paste("批量提取完成，成功:", successful_count, "失败:", failed_count))

      return(results)
    },

    # 将EIC数据转换为前端友好的JSON格式
    format_eic_data_for_frontend = function(eic_data) {
      if (is.null(eic_data)) {
        return(NULL)
      }

      # 按文件组织EIC数据点
      eic_points_by_file <- list()

      if (nrow(eic_data$ms1_peaks) > 0) {
        for (i in 1:nrow(eic_data$ms1_peaks)) {
          peak <- eic_data$ms1_peaks[i, ]
          file_id <- peak$file_id

          if (is.null(eic_points_by_file[[file_id]])) {
            eic_points_by_file[[file_id]] <- list(
              file_info = eic_data$matched_files[eic_data$matched_files$file_id == file_id, ][1, ],
              eic_points = list()
            )
          }

          eic_point <- list(
            peak_id = peak$peak_id,
            spectrum_id = peak$spectrum_id,
            rtime = peak$rtime,
            mz = peak$mz,
            intensity = peak$intensity,
            mz_error_ppm = peak$mz_error_ppm,
            scan_index = peak$scan_index
          )

          eic_points_by_file[[file_id]]$eic_points[[length(eic_points_by_file[[file_id]]$eic_points) + 1]] <- eic_point
        }
      }

      # 组织MS2数据
      ms2_data_by_peak <- list()

      if (nrow(eic_data$ms2_spectra) > 0) {
        for (i in 1:nrow(eic_data$ms2_spectra)) {
          ms2_spectrum <- eic_data$ms2_spectra[i, ]
          peak_id <- as.character(ms2_spectrum$ms1_peak_id)

          if (is.null(ms2_data_by_peak[[peak_id]])) {
            ms2_data_by_peak[[peak_id]] <- list()
          }

          # 查找对应的碎片离子
          fragments <- eic_data$ms2_fragments[eic_data$ms2_fragments$spectrum_id == ms2_spectrum$spectrum_id, ]

          fragment_list <- list()
          if (nrow(fragments) > 0) {
            for (j in 1:nrow(fragments)) {
              fragment_list[[j]] <- list(
                mz = fragments[j, "mz"],
                intensity = fragments[j, "intensity"]
              )
            }
          }

          ms2_info <- list(
            spectrum_id = ms2_spectrum$spectrum_id,
            rtime = ms2_spectrum$rtime,
            precursor_mz = ms2_spectrum$precursor_mz,
            precursor_intensity = ms2_spectrum$precursor_intensity,
            precursor_charge = ms2_spectrum$precursor_charge,
            collision_energy = ms2_spectrum$collision_energy,
            isolation_window_lower_mz = ms2_spectrum$isolation_window_lower_mz,
            isolation_window_target_mz = ms2_spectrum$isolation_window_target_mz,
            isolation_window_upper_mz = ms2_spectrum$isolation_window_upper_mz,
            fragments = fragment_list
          )

          ms2_data_by_peak[[peak_id]][[length(ms2_data_by_peak[[peak_id]]) + 1]] <- ms2_info
        }
      }

      # 构建最终的JSON结构
      frontend_data <- list(
        isotope_info = eic_data$isotope_info,
        eic_data_by_file = eic_points_by_file,
        ms2_data_by_peak = ms2_data_by_peak,
        summary = list(
          total_files = length(eic_points_by_file),
          total_eic_points = nrow(eic_data$ms1_peaks),
          total_ms2_spectra = nrow(eic_data$ms2_spectra),
          total_ms2_fragments = nrow(eic_data$ms2_fragments)
        ),
        extraction_info = list(
          extraction_time = as.character(eic_data$extraction_time),
          extraction_parameters = eic_data$extraction_parameters
        )
      )

      return(frontend_data)
    }
  )
)

# 空值合并操作符
`%||%` <- function(x, y) {
  if (is.null(x) || length(x) == 0 || (is.character(x) && x == "")) y else x
}

# 创建EIC提取器实例
create_eic_extractor <- function(db_path = NULL, tolerance_ppm = 20, min_intensity = 1000) {
  load_eic_extractor_packages()
  return(IsotopeEICExtractor$new(db_path, tolerance_ppm, min_intensity))
}

# 从项目数据库提取同位素内标EIC数据
extract_isotope_eic_from_project <- function(isotope_ions_data, output_file = NULL,
                                           tolerance_ppm = 20, min_intensity = 1000) {
  tryCatch({
    # 获取项目数据库路径
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      stop("项目路径未设置")
    }

    db_path <- file.path(project_root, "data", "spectra.db")
    if (!file.exists(db_path)) {
      stop("项目数据库不存在:", db_path)
    }

    # 创建EIC提取器
    extractor <- create_eic_extractor(db_path, tolerance_ppm, min_intensity)

    # 转换数据格式
    isotope_ions_list <- list()

    if (is.data.frame(isotope_ions_data)) {
      # 从数据框转换
      for (i in 1:nrow(isotope_ions_data)) {
        row <- isotope_ions_data[i, ]
        isotope_ions_list[[i]] <- list(
          化合物名称 = row$化合物名称 %||% paste("化合物", i),
          分子质量 = row$分子质量 %||% 0,
          保留时间 = row$保留时间 %||% 0,
          离子化模式 = row$离子化模式 %||% "positive",
          离子质荷比 = row$离子质荷比 %||% 0,
          离子类型 = row$离子类型 %||% "[M+H]+",
          备注 = row$备注 %||% ""
        )
      }
    } else if (is.list(isotope_ions_data)) {
      # 直接使用列表
      isotope_ions_list <- isotope_ions_data
    } else {
      stop("不支持的数据格式")
    }

    # 批量提取EIC数据
    eic_results <- extractor$extract_multiple_isotope_eic(isotope_ions_list)

    # 转换为前端友好格式
    frontend_results <- list()
    for (compound_name in names(eic_results)) {
      frontend_results[[compound_name]] <- extractor$format_eic_data_for_frontend(eic_results[[compound_name]])
    }

    # 构建最终结果
    final_results <- list(
      metadata = list(
        extraction_time = as.character(Sys.time()),
        total_compounds = length(frontend_results),
        extraction_parameters = list(
          tolerance_ppm = tolerance_ppm,
          min_intensity = min_intensity
        ),
        database_path = db_path
      ),
      compounds = frontend_results
    )

    # 保存到文件（如果指定了输出文件）
    if (!is.null(output_file)) {
      # 确保输出目录存在
      output_dir <- dirname(output_file)
      if (!dir.exists(output_dir)) {
        dir.create(output_dir, recursive = TRUE)
      }

      # 保存为JSON文件
      json_content <- jsonlite::toJSON(final_results, auto_unbox = TRUE, pretty = TRUE)
      writeLines(json_content, output_file, useBytes = TRUE)

      log_info(paste("EIC数据已保存到:", output_file))
    }

    # 断开数据库连接
    extractor$disconnect_database()

    log_info(paste("同位素内标EIC数据提取完成，共处理", length(frontend_results), "个化合物"))

    return(final_results)

  }, error = function(e) {
    log_error(paste("提取同位素内标EIC数据失败:", e$message))
    return(NULL)
  })
}

# 从监控离子数据文件提取EIC数据
extract_eic_from_monitor_ions_file <- function(monitor_ions_file = NULL, output_file = NULL,
                                              tolerance_ppm = 20, min_intensity = 1000) {
  # 如果未指定文件，使用项目默认路径
  if (is.null(monitor_ions_file)) {
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      stop("项目路径未设置")
    }
    monitor_ions_file <- file.path(project_root, "data", "monitor_ions_data.yaml")
  }

  if (!file.exists(monitor_ions_file)) {
    stop("监控离子数据文件不存在:", monitor_ions_file)
  }

  # 读取监控离子数据
  yaml_content <- yaml::read_yaml(monitor_ions_file, fileEncoding = "UTF-8")

  if (is.null(yaml_content$monitor_ions_data) || length(yaml_content$monitor_ions_data) == 0) {
    stop("监控离子数据文件为空或格式不正确")
  }

  # 过滤出同位素内标（根据化合物名称或备注判断）
  isotope_ions <- list()
  for (ion_data in yaml_content$monitor_ions_data) {
    compound_name <- ion_data$化合物名称 %||% ""
    notes <- ion_data$备注 %||% ""

    # 简单的同位素内标识别逻辑（可以根据需要调整）
    if (grepl("同位素|内标|isotope|internal", compound_name, ignore.case = TRUE) ||
        grepl("同位素|内标|isotope|internal", notes, ignore.case = TRUE)) {
      isotope_ions[[length(isotope_ions) + 1]] <- ion_data
    }
  }

  if (length(isotope_ions) == 0) {
    log_warning("未找到同位素内标离子")
    return(NULL)
  }

  log_info(paste("找到", length(isotope_ions), "个同位素内标离子"))

  # 提取EIC数据
  return(extract_isotope_eic_from_project(isotope_ions, output_file, tolerance_ppm, min_intensity))
}
